# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "0751e3713f9f29e2d8f3609e4fa902e1"
name = "Khodkar"
handle = "khodkar"
application_url = "https://localhost:3458"
embedded = true

[build]
automatically_update_urls_on_dev = true
include_config_on_deploy = true

[webhooks]
api_version = "2025-07"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "customer_read_customers,customer_read_orders,customer_read_store_credit_account_transactions,customer_read_store_credit_accounts,unauthenticated_read_product_listings"

[auth]
redirect_urls = ["https://localhost:3458/auth/callback", "https://localhost:3458/auth/shopify/callback", "https://localhost:3458/api/auth/callback"]

[pos]
embedded = false
