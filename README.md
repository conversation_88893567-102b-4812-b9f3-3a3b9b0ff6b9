# Khodkar Shopify

This is a Shopify app that lets you to put a Khodkar chat bubble on your shop for customer support and sales.

## Internal design

### Tech stack

The Khodkar Shopify makes use of these technologies:

1. Remix
2. Shopify CLI
3. Shopify Polaris
4. Prisma
5. GraphQL
6. Shopify JS SDK
7. React JS
8. Liquid
9. Alpine.js

### Architecture

```mermaid
flowchart TD
    S[Shopify] <-->|Installation, payments and MCP| A(Khodkar App)
    S <-->|Injection of chat bubble and admin panel Iframe| B(Khodkar app front-end and extension)
    B <--> A
    K(Khodkar Core) <-->|User managment and AI requests| A
```

>[!NOTE]
> To update the [mermaid](https://mermaid.js.org/) diagram above, consider using it's [online editor](https://mermaid.live).

## How to run the project?
### Configs
