/**
 * Simple test to verify the chat endpoint responds without errors
 */

async function testSimple() {
  const testMessage = "Hello";
  const conversationId = `test_${Date.now()}`;
  
  console.log('Testing simple chat message...');
  
  try {
    const response = await fetch('http://localhost:3460/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream',
      },
      body: JSON.stringify({
        message: testMessage,
        conversation_id: conversationId
      })
    });
    
    console.log('Response status:', response.status);
    
    if (!response.ok) {
      const text = await response.text();
      console.log('Error response:', text);
      return;
    }
    
    // Read just the first few chunks to see if there are any immediate errors
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    
    let chunkCount = 0;
    const maxChunks = 5; // Only read first few chunks
    
    while (chunkCount < maxChunks) {
      const { done, value } = await reader.read();
      
      if (done) {
        console.log('Stream ended early');
        break;
      }
      
      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = line.slice(6);
          if (data.trim()) {
            try {
              const parsed = JSON.parse(data);
              console.log('Received:', parsed.type, parsed.error ? `- ERROR: ${parsed.error}` : '');
              
              // If we get an error, stop reading
              if (parsed.type === 'error') {
                reader.cancel();
                return;
              }
            } catch (e) {
              console.log('Raw data:', data);
            }
          }
        }
      }
      
      chunkCount++;
    }
    
    // Cancel the reader to stop the stream
    reader.cancel();
    console.log('Test completed - no immediate errors detected');
    
  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

// Run the test
testSimple();
