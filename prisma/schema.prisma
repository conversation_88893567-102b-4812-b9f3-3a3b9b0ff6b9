// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

// Note that some adapters may set a maximum length for the String type by default, please ensure your strings are long
// enough when changing adapters.
// See https://www.prisma.io/docs/orm/reference/prisma-schema-reference#string for more information
datasource db {
  provider = "sqlite"
  url      = "file:dev.sqlite"
}

model Session {
  id            String    @id
  shop          String
  state         String
  isOnline      Boolean   @default(false)
  scope         String?
  expires       DateTime?
  accessToken   String
  userId        BigInt?
  firstName     String?
  lastName      String?
  email         String?
  accountOwner  Boolean   @default(false)
  locale        String?
  collaborator  <PERSON><PERSON><PERSON>?  @default(false)
  emailVerified <PERSON>olean?  @default(false)
}

model CustomerToken {
  id              String    @id
  conversationId  String
  accessToken     String
  refreshToken    String?
  expiresAt       DateTime
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  @@index([conversationId])
}

model CodeVerifier {
  id              String    @id
  state           String    @unique
  verifier        String
  createdAt       DateTime  @default(now())
  expiresAt       DateTime

  @@index([state])
}

model Conversation {
  id        String    @id
  messages  Message[]
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Message {
  id             String       @id @default(cuid())
  conversationId String
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  role           String       // "user" or "assistant"
  content        String
  createdAt      DateTime     @default(now())

  @@index([conversationId])
}

model CustomerAccountUrl {
  id             String    @id @default(cuid())
  conversationId String    @unique
  url            String
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
}
