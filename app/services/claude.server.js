/**
 * Claude Service
 * Manages interactions with AI models via OpenRouter
 */
import { createOpenAICompatible } from '@ai-sdk/openai-compatible';
import { streamText } from "ai";
import AppConfig from "./config.server.js";
import systemPrompts from "../prompts/prompts.json" with { type: "json" };

/**
 * Creates a Claude service instance
 * @param {string} apiKey - OpenRouter API key
 * @param {string} modelName - Model name to use (defaults to config)
 * @returns {Object} Claude service with methods for interacting with AI models via OpenRouter
 */
export function createClaudeService(apiKey = AppConfig.api.openRouter.apiKey, modelName = AppConfig.api.defaultModel) {
  // Initialize OpenRouter client with AI SDK
  const openRouter = createOpenAICompatible({
    baseURL: AppConfig.api.openRouter.baseURL,
    apiKey: apiKey,
    name: 'openrouter',
  });

  const model = openRouter(modelName);

  /**
   * Converts and validates messages for AI SDK compatibility
   * @param {Array} messages - Raw messages from database/UI
   * @returns {Array} AI SDK compatible ModelMessage array
   */
  const convertAndValidateMessages = (messages) => {
    if (!messages || !Array.isArray(messages)) {
      return [];
    }

    // Convert messages to simple ModelMessage format directly
    const modelMessages = messages.map(msg => {
      // Ensure we have a valid message structure
      if (!msg || typeof msg !== 'object') {
        console.warn('Invalid message structure:', msg);
        return null;
      }

      // Validate role
      const validRoles = ['user', 'assistant', 'system', 'tool'];
      if (!validRoles.includes(msg.role)) {
        console.warn('Invalid message role:', msg.role);
        return null;
      }

      // Handle content conversion
      let content = msg.content;

      // If content is a number, convert to string
      if (typeof content === 'number') {
        content = content.toString();
      }

      // If content is an array (assistant messages), extract text
      if (Array.isArray(content)) {
        // For assistant messages with structured content
        const textParts = content
          .filter(part => part && part.type === 'text')
          .map(part => part.text)
          .filter(text => typeof text === 'string');

        content = textParts.join('');
      }

      // Ensure content is a string
      if (typeof content !== 'string') {
        console.warn('Invalid content type, converting to string:', typeof content, content);
        content = String(content || '');
      }

      // Return simple ModelMessage format
      return {
        role: msg.role,
        content: content
      };
    }).filter(msg => msg !== null); // Remove invalid messages

    return modelMessages;
  };

  /**
   * Converts MCP tools to AI SDK format
   * @param {Array} mcpTools - MCP tools with input_schema format
   * @returns {Object} AI SDK compatible tools object
   */
  const convertMcpToolsToAiSdk = (mcpTools) => {
    if (!mcpTools || !Array.isArray(mcpTools)) {
      return {};
    }

    const aiSdkTools = {};

    for (const mcpTool of mcpTools) {
      try {
        // Validate that the tool has required properties
        if (!mcpTool.name || !mcpTool.description) {
          console.warn('Skipping invalid MCP tool:', mcpTool);
          continue;
        }

        // Get the input schema, ensuring it's a valid object
        let inputSchema = mcpTool.input_schema || mcpTool.inputSchema;

        // If no schema provided, create a basic one
        if (!inputSchema || typeof inputSchema !== 'object') {
          inputSchema = {
            type: 'object',
            properties: {},
            required: []
          };
        }

        aiSdkTools[mcpTool.name] = {
          description: mcpTool.description,
          parameters: inputSchema, // Use 'parameters' instead of 'inputSchema' for AI SDK
          // Note: execute function is handled by MCP client, not needed here
        };
      } catch (error) {
        console.warn('Error converting MCP tool:', mcpTool.name, error);
      }
    }

    return aiSdkTools;
  };

  /**
   * Streams a conversation with Claude
   * @param {Object} params - Stream parameters
   * @param {Array} params.messages - Conversation history
   * @param {string} params.promptType - The type of system prompt to use
   * @param {Array} params.tools - Available tools for Claude (MCP format)
   * @param {Object} streamHandlers - Stream event handlers
   * @param {Function} streamHandlers.onText - Handles text chunks
   * @param {Function} streamHandlers.onMessage - Handles complete messages
   * @param {Function} streamHandlers.onToolUse - Handles tool use requests
   * @returns {Promise<Object>} The final message
   */
  const streamConversation = async ({
    messages,
    promptType = AppConfig.api.defaultPromptType,
    tools
  }, streamHandlers) => {
    // Get system prompt from configuration or use default
    const systemInstruction = getSystemPrompt(promptType);

    try {
      // Convert and validate messages for AI SDK compatibility
      const modelMessages = convertAndValidateMessages(messages);

      // Convert MCP tools to AI SDK format
      const aiSdkTools = convertMcpToolsToAiSdk(tools);

      // Create stream using AI SDK
      const result = streamText({
        model: model,
        system: systemInstruction,
        messages: modelMessages,
        maxTokens: AppConfig.api.maxTokens,
        // tools: Object.keys(aiSdkTools).length > 0 ? aiSdkTools : undefined,
      });

      let fullText = '';

      // Process the stream
      for await (const delta of result.textStream) {
        fullText += delta;
        if (streamHandlers.onText) {
          streamHandlers.onText(delta);
        }
      }

      // Get the final results using the promise properties
      const [finishReason, toolCalls] = await Promise.all([
        result.finishReason,
        result.toolCalls
      ]);

      // Create a message object compatible with the existing interface
      const finalMessage = {
        role: 'assistant',
        content: [{ type: 'text', text: fullText }],
        stop_reason: finishReason === 'stop' ? 'end_turn' : finishReason
      };

      // Handle tool calls if present
      if (toolCalls && toolCalls.length > 0) {
        for (const toolCall of toolCalls) {
          const toolContent = {
            type: 'tool_use',
            id: toolCall.toolCallId,
            name: toolCall.toolName,
            input: toolCall.args
          };
          finalMessage.content.push(toolContent);

          if (streamHandlers.onToolUse) {
            await streamHandlers.onToolUse(toolContent);
          }
        }
      }

      // Call onMessage handler
      if (streamHandlers.onMessage) {
        streamHandlers.onMessage(finalMessage);
      }

      return finalMessage;

    } catch (error) {
      console.error('Error in streamConversation:', error);
      // Create error message
      const errorMessage = {
        role: 'assistant',
        content: [{ type: 'text', text: 'Sorry, I encountered an error processing your request.' }],
        stop_reason: 'error'
      };

      if (streamHandlers.onMessage) {
        streamHandlers.onMessage(errorMessage);
      }

      return errorMessage;
    }
  };

  /**
   * Gets the system prompt content for a given prompt type
   * @param {string} promptType - The prompt type to retrieve
   * @returns {string} The system prompt content
   */
  const getSystemPrompt = (promptType) => {
    return systemPrompts.systemPrompts[promptType]?.content ||
      systemPrompts.systemPrompts[AppConfig.api.defaultPromptType].content;
  };

  return {
    streamConversation,
    getSystemPrompt
  };
}

export default {
  createClaudeService
};
